# Node-Sass WordPress Development Workflow

This project provides a centralized Sass/SCSS compilation system for multiple WordPress websites using Gulp. Instead of setting up build tools for each individual website, this single node-sass project handles CSS compilation for all your WordPress sites.

## How It Works

The workflow is designed around **one-click development setup** using individual `.bat` files for each website:

1. **Create a `.bat` file** for each WordPress website
2. **Double-click the `.bat` file** when you want to work on that site
3. **Gulp automatically compiles and watches** your SCSS files for changes

## Project Structure

```
_node-sass/
├── gulpfile.js                                    # Main Gulp configuration
├── package.json                                   # Node.js dependencies
├── gulp. AMP node scss .bat                       # Batch file for AMP site
├── gulp. DC.com node scss doug cassidy sass.bat   # Batch file for Doug Cassidy site
├── gulp. kdug node scss .bat                      # Batch file for KDUG Radio site
├── gulp.salmon soldiers node scss.bat             # Batch file for Salmon Soldiers site
└── .cache/                                        # Auto-generated cache directory
```

## Setting Up a New Website

### 1. Create a New .bat File

Copy an existing `.bat` file and modify the variables at the top:

```batch
@echo off
:: Run this from the node-sass folder

:: Set vars, these should be all you need to set
set SITENAME=your-website.com
set CHILDNAME=d_theme_3_child
set ROOTDIRNAME=www

:: Set output format for main CSS file (debug, minified, clean)
:: debug = expanded CSS with sourcemaps in style.css
:: minified = compressed CSS in style.css (default)
:: clean = readable CSS without debug info in style.css
:: Note: style.debug.css with sourcemaps is always generated regardless of this setting
set OUTPUTFORMAT=minified

:: THESE SHOULD BE GOOD
set PATHTOTHEMES=C:\_d\websites\%SITENAME%\%ROOTDIRNAME%\wp-content\themes\
set PATHTODINCGALLERY=C:\_d\websites\%SITENAME%\%ROOTDIRNAME%\wp-content/mu-plugins/d_inc/d_gallery/css/partials

:: Set output path for compiled CSS files
set THEME_PATH=%PATHTOTHEMES%%CHILDNAME%

:: Set watch paths (semicolon-separated list of directories to watch)
set WATCH_PATHS=%PATHTOTHEMES%%CHILDNAME%\css
set WATCH_PATHS=%WATCH_PATHS%;%PATHTOTHEMES%d_theme_3\css
set WATCH_PATHS=%WATCH_PATHS%;%PATHTODINCGALLERY%

:: Add specific files to watch for import changes
set WATCH_FILES=%PATHTOTHEMES%%CHILDNAME%\css\style.scss

echo Running Gulp for %SITENAME% ...
:: Run gulp with the local gulpfile
node node_modules\gulp\bin\gulp.js --gulpfile gulpfile.js %*

:: Pause to see any error messages
if %ERRORLEVEL% neq 0 (
  echo Error occurred! Error level: %ERRORLEVEL%
  pause
)
```

### 2. Configure the Variables

**Required Variables to Change:**
- `SITENAME`: Your website domain (e.g., `example.com`)
- `CHILDNAME`: Your child theme folder name (usually `d_theme_3_child`)
- `ROOTDIRNAME`: WordPress root folder name (usually `www`)

**Optional Variables:**
- `PATHTOTHEMES`: Path to WordPress themes directory
- `PATHTODINCGALLERY`: Path to gallery CSS partials (if using d_inc gallery)

### 3. Name Your .bat File

Use a descriptive name like:
- `gulp. [SITENAME] node scss .bat`
- `gulp. [PROJECT NAME] node scss .bat`

## How to Use

### Starting Development

1. **Double-click** the `.bat` file for the website you want to work on
2. A command prompt window will open showing:
   - Output directory path
   - Directories being watched
   - SCSS files found
   - Compilation status

### What Happens Automatically

The Gulp process will:

1. **Compile SCSS to CSS** in two formats:
   - `style.debug.css` - Expanded CSS with source maps for debugging
   - `style.css` - Minified CSS for production

2. **Watch for changes** in:
   - All SCSS files in specified directories
   - Import statements in main style files
   - Nested directories (automatically discovered)

3. **Auto-recompile** whenever you save changes to any watched SCSS file

### Stopping Development

- Close the command prompt window, or
- Press `Ctrl+C` in the command prompt

## Features

### Intelligent File Watching
- Automatically finds all SCSS files in specified directories
- Watches for changes in `@import` statements
- Rebuilds when import dependencies change
- Caches import information to optimize performance

### Dual Output Format
- **Debug version** (`style.debug.css`): Expanded CSS with source maps
- **Production version** (`style.css`): Minified and optimized CSS

### Cross-Project Cache Management
- Each project gets its own cache directory
- Import change detection prevents unnecessary rebuilds
- Cache survives between development sessions

### Error Handling
- Detailed error messages for compilation issues
- Process pauses on errors so you can read them
- Continues watching after fixing errors

## Troubleshooting

### Common Issues

**"Directory not found" warnings:**
- Check that your `SITENAME` and paths are correct
- Ensure the WordPress site exists at the specified location

**No SCSS files found:**
- Verify the `WATCH_PATHS` include your SCSS directories
- Check that SCSS files exist in the specified locations

**Compilation errors:**
- Review SCSS syntax in the error message
- Check `@import` paths are correct
- Ensure all imported files exist

### File Paths

This system assumes your WordPress sites are organized like:
```
C:\_d\websites\
├── site1.com\
│   └── www\
│       └── wp-content\
│           └── themes\
│               ├── d_theme_3\
│               └── d_theme_3_child\
├── site2.com\
│   └── www\
│       └── wp-content\
│           └── themes\
│               ├── d_theme_3\
│               └── d_theme_3_child\
```

Adjust the `PATHTOTHEMES` variable if your structure is different.

## Dependencies

The project uses these main tools:
- **Gulp 5.0** - Task runner
- **Sass** - SCSS compiler
- **gulp-clean-css** - CSS minification
- **gulp-sourcemaps** - Source map generation
- **gulp-watch** - File watching

All dependencies are already installed in `node_modules/`.

## Tips

- Keep the command prompt window open while developing to see compilation status
- Use meaningful names for your `.bat` files to easily identify projects
- The debug CSS file includes source maps for easier browser debugging
- Import changes are automatically detected and trigger full rebuilds
