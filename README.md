# Node-Sass Build System

This is a Gulp-based build system for compiling SCSS files with multiple compilation modes.

## Compilation Modes

The build system supports three different compilation modes:

### 1. Debug Mode (`--mode=debug`)
- **Output**: `style.debug.css` + `style.debug.css.map`
- **Features**: 
  - Expanded CSS output with proper formatting
  - Source maps for debugging
  - No minification
- **Use case**: Development and debugging

### 2. Minified Mode (`--mode=minified`)
- **Output**: `style.css`
- **Features**:
  - Compressed CSS output
  - CleanCSS optimization
  - No source maps
  - Smallest file size
- **Use case**: Production deployment

### 3. Clean Mode (`--mode=clean`)
- **Output**: `style.clean.css`
- **Features**:
  - Expanded CSS output with line breaks
  - No source maps
  - No minification
  - Clean, readable CSS
- **Use case**: When you need readable CSS without debug overhead

## Usage

### Command Line Options

```bash
# Compile in debug mode
gulp --mode=debug

# Compile in minified mode
gulp --mode=minified

# Compile in clean mode
gulp --mode=clean

# Compile all modes at once
gulp --mode=all

# Default behavior (debug + minified)
gulp
```

### Available Tasks

```bash
# Individual compilation tasks
gulp debug      # Compile debug version only
gulp minified   # Compile minified version only
gulp clean      # Compile clean version only
gulp all        # Compile all three versions

# Utility tasks
gulp compile    # Compile using the specified --mode
gulp watch      # Watch files and compile using specified mode
```

### Watch Mode

The watch functionality respects the compilation mode:

```bash
# Watch and compile in debug mode
gulp --mode=debug

# Watch and compile in clean mode
gulp --mode=clean
```

## Environment Variables

- `THEME_PATH`: Path to the theme directory (default: current directory)
- `WATCH_PATHS`: Semicolon-separated list of directories to watch for SCSS files
- `WATCH_FILES`: Semicolon-separated list of specific files to watch for import changes
- `SITENAME`: Site name for logging purposes

## File Structure

```
your-theme/
├── css/
│   └── style.scss          # Main SCSS file
├── style.debug.css         # Debug output (with source maps)
├── style.debug.css.map     # Source map file
├── style.css               # Minified output
└── style.clean.css         # Clean output
```

## Examples

```bash
# Development workflow
gulp --mode=debug

# Production build
gulp --mode=minified

# Generate readable CSS for review
gulp --mode=clean

# Build all versions for testing
gulp --mode=all
```
