{"version": 3, "sources": ["data:;charset=utf-8,/%20Sample%20SCSS%20file%20for%20testing%20compilation%20modes%0A$primary-color:%20%233498db;%0A$secondary-color:%20%232ecc71;%0A$font-size-base:%2016px;%0A%0A/%20Mixins%0A@mixin%20button-style($bg-color,%20$text-color:%20white)%20%7B%0A%20%20background-color:%20$bg-color;%0A%20%20color:%20$text-color;%0A%20%20padding:%2010px%2020px;%0A%20%20border:%20none;%0A%20%20border-radius:%204px;%0A%20%20cursor:%20pointer;%0A%20%20%0A%20%20&:hover%20%7B%0A%20%20%20%20background-color:%20darken($bg-color,%2010%25);%0A%20%20%7D%0A%7D%0A%0A/%20Base%20styles%0Abody%20%7B%0A%20%20font-family:%20'Arial',%20sans-serif;%0A%20%20font-size:%20$font-size-base;%0A%20%20line-height:%201.6;%0A%20%20margin:%200;%0A%20%20padding:%200;%0A%20%20background-color:%20%23f8f9fa;%0A%7D%0A%0A.container%20%7B%0A%20%20max-width:%201200px;%0A%20%20margin:%200%20auto;%0A%20%20padding:%200%2020px;%0A%7D%0A%0A/%20Header%20styles%0A.header%20%7B%0A%20%20background-color:%20$primary-color;%0A%20%20color:%20white;%0A%20%20padding:%201rem%200;%0A%20%20%0A%20%20h1%20%7B%0A%20%20%20%20margin:%200;%0A%20%20%20%20font-size:%202rem;%0A%20%20%7D%0A%20%20%0A%20%20nav%20%7B%0A%20%20%20%20ul%20%7B%0A%20%20%20%20%20%20list-style:%20none;%0A%20%20%20%20%20%20padding:%200;%0A%20%20%20%20%20%20margin:%200;%0A%20%20%20%20%20%20display:%20flex;%0A%20%20%20%20%20%20%0A%20%20%20%20%20%20li%20%7B%0A%20%20%20%20%20%20%20%20margin-right:%2020px;%0A%20%20%20%20%20%20%20%20%0A%20%20%20%20%20%20%20%20a%20%7B%0A%20%20%20%20%20%20%20%20%20%20color:%20white;%0A%20%20%20%20%20%20%20%20%20%20text-decoration:%20none;%0A%20%20%20%20%20%20%20%20%20%20%0A%20%20%20%20%20%20%20%20%20%20&:hover%20%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20text-decoration:%20underline;%0A%20%20%20%20%20%20%20%20%20%20%7D%0A%20%20%20%20%20%20%20%20%7D%0A%20%20%20%20%20%20%7D%0A%20%20%20%20%7D%0A%20%20%7D%0A%7D%0A%0A/%20Button%20styles%0A.btn%20%7B%0A%20%20@include%20button-style($primary-color);%0A%20%20%0A%20%20&.btn-secondary%20%7B%0A%20%20%20%20@include%20button-style($secondary-color);%0A%20%20%7D%0A%20%20%0A%20%20&.btn-large%20%7B%0A%20%20%20%20padding:%2015px%2030px;%0A%20%20%20%20font-size:%201.2rem;%0A%20%20%7D%0A%7D%0A%0A/%20Content%20styles%0A.content%20%7B%0A%20%20padding:%202rem%200;%0A%20%20%0A%20%20.card%20%7B%0A%20%20%20%20background:%20white;%0A%20%20%20%20border-radius:%208px;%0A%20%20%20%20box-shadow:%200%202px%2010px%20rgba(0,%200,%200,%200.1);%0A%20%20%20%20padding:%201.5rem;%0A%20%20%20%20margin-bottom:%201rem;%0A%20%20%20%20%0A%20%20%20%20h2%20%7B%0A%20%20%20%20%20%20color:%20$primary-color;%0A%20%20%20%20%20%20margin-top:%200;%0A%20%20%20%20%7D%0A%20%20%20%20%0A%20%20%20%20p%20%7B%0A%20%20%20%20%20%20color:%20%23666;%0A%20%20%20%20%20%20line-height:%201.8;%0A%20%20%20%20%7D%0A%20%20%7D%0A%7D%0A%0A/%20Footer%20styles%0A.footer%20%7B%0A%20%20background-color:%20%23343a40;%0A%20%20color:%20white;%0A%20%20text-align:%20center;%0A%20%20padding:%201rem%200;%0A%20%20margin-top:%202rem;%0A%7D%0A%0A/%20Responsive%20design%0A@media%20(max-width:%20768px)%20%7B%0A%20%20.container%20%7B%0A%20%20%20%20padding:%200%2010px;%0A%20%20%7D%0A%20%20%0A%20%20.header%20%7B%0A%20%20%20%20nav%20ul%20%7B%0A%20%20%20%20%20%20flex-direction:%20column;%0A%20%20%20%20%20%20%0A%20%20%20%20%20%20li%20%7B%0A%20%20%20%20%20%20%20%20margin-right:%200;%0A%20%20%20%20%20%20%20%20margin-bottom:%2010px;%0A%20%20%20%20%20%20%7D%0A%20%20%20%20%7D%0A%20%20%7D%0A%20%20%0A%20%20.btn%20%7B%0A%20%20%20%20display:%20block;%0A%20%20%20%20width:%20100%25;%0A%20%20%20%20margin-bottom:%2010px;%0A%20%20%7D%0A%7D%0A"], "names": [], "mappings": "AAoBA;EACE;EACA,WAnBe;EAoBf;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAIF;EACE,kBApCc;EAqCd;EACA;;AAEA;EACE;EACA;;AAIA;EACE;EACA;EACA;EACA;;AAEA;EACE;;AAEA;EACE;EACA;;AAEA;EACE;;;AASZ;EA/DE,kBANc;EAOd,OAF0C;EAG1C;EACA;EACA;EACA;;AAEA;EACE;;AA0DF;EAlEA,kBALgB;EAMhB,OAF0C;EAG1C;EACA;EACA;EACA;;AAEA;EACE;;AA8DF;EACE;EACA;;;AAKJ;EACE;;AAEA;EACE;EACA;EACA;EACA;EACA;;AAEA;EACE,OA9FU;EA+FV;;AAGF;EACE;EACA;;;AAMN;EACE;EACA;EACA;EACA;EACA;;;AAIF;EACE;IACE;;EAIA;IACE;;EAEA;IACE;IACA;;EAKN;IACE;IACA;IACA", "sourcesContent": ["// Sample SCSS file for testing compilation modes\n$primary-color: #3498db;\n$secondary-color: #2ecc71;\n$font-size-base: 16px;\n\n// Mixins\n@mixin button-style($bg-color, $text-color: white) {\n  background-color: $bg-color;\n  color: $text-color;\n  padding: 10px 20px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  \n  &:hover {\n    background-color: darken($bg-color, 10%);\n  }\n}\n\n// Base styles\nbody {\n  font-family: 'Arial', sans-serif;\n  font-size: $font-size-base;\n  line-height: 1.6;\n  margin: 0;\n  padding: 0;\n  background-color: #f8f9fa;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n}\n\n// Header styles\n.header {\n  background-color: $primary-color;\n  color: white;\n  padding: 1rem 0;\n  \n  h1 {\n    margin: 0;\n    font-size: 2rem;\n  }\n  \n  nav {\n    ul {\n      list-style: none;\n      padding: 0;\n      margin: 0;\n      display: flex;\n      \n      li {\n        margin-right: 20px;\n        \n        a {\n          color: white;\n          text-decoration: none;\n          \n          &:hover {\n            text-decoration: underline;\n          }\n        }\n      }\n    }\n  }\n}\n\n// Button styles\n.btn {\n  @include button-style($primary-color);\n  \n  &.btn-secondary {\n    @include button-style($secondary-color);\n  }\n  \n  &.btn-large {\n    padding: 15px 30px;\n    font-size: 1.2rem;\n  }\n}\n\n// Content styles\n.content {\n  padding: 2rem 0;\n  \n  .card {\n    background: white;\n    border-radius: 8px;\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n    padding: 1.5rem;\n    margin-bottom: 1rem;\n    \n    h2 {\n      color: $primary-color;\n      margin-top: 0;\n    }\n    \n    p {\n      color: #666;\n      line-height: 1.8;\n    }\n  }\n}\n\n// Footer styles\n.footer {\n  background-color: #343a40;\n  color: white;\n  text-align: center;\n  padding: 1rem 0;\n  margin-top: 2rem;\n}\n\n// Responsive design\n@media (max-width: 768px) {\n  .container {\n    padding: 0 10px;\n  }\n  \n  .header {\n    nav ul {\n      flex-direction: column;\n      \n      li {\n        margin-right: 0;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  \n  .btn {\n    display: block;\n    width: 100%;\n    margin-bottom: 10px;\n  }\n}\n"], "file": "style.debug.css"}