// Sample SCSS file for testing compilation modes
$primary-color: #3498db;
$secondary-color: #2ecc71;
$font-size-base: 16px;

// Mixins
@mixin button-style($bg-color, $text-color: white) {
  background-color: $bg-color;
  color: $text-color;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    background-color: darken($bg-color, 10%);
  }
}

// Base styles
body {
  font-family: 'Arial', sans-serif;
  font-size: $font-size-base;
  line-height: 1.6;
  margin: 0;
  padding: 0;
  background-color: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

// Header styles
.header {
  background-color: $primary-color;
  color: white;
  padding: 1rem 0;
  
  h1 {
    margin: 0;
    font-size: 2rem;
  }
  
  nav {
    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      
      li {
        margin-right: 20px;
        
        a {
          color: white;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}

// Button styles
.btn {
  @include button-style($primary-color);
  
  &.btn-secondary {
    @include button-style($secondary-color);
  }
  
  &.btn-large {
    padding: 15px 30px;
    font-size: 1.2rem;
  }
}

// Content styles
.content {
  padding: 2rem 0;
  
  .card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1rem;
    
    h2 {
      color: $primary-color;
      margin-top: 0;
    }
    
    p {
      color: #666;
      line-height: 1.8;
    }
  }
}

// Footer styles
.footer {
  background-color: #343a40;
  color: white;
  text-align: center;
  padding: 1rem 0;
  margin-top: 2rem;
}

// Responsive design
@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }
  
  .header {
    nav ul {
      flex-direction: column;
      
      li {
        margin-right: 0;
        margin-bottom: 10px;
      }
    }
  }
  
  .btn {
    display: block;
    width: 100%;
    margin-bottom: 10px;
  }
}
