@echo off
:: Run this from the node-sass folder

:: Set vars, these should be all you need to set
set SITENAME=dougcassidy.com
set CHILDNAME=d_theme_3_child
set ROOTDIRNAME=www

:: Set output format for main CSS file (debug, minified, clean)
:: debug = expanded CSS with sourcemaps in style.css
:: minified = compressed CSS in style.css (default)
:: clean = readable CSS without debug info in style.css
:: Note: style.debug.css with sourcemaps is always generated regardless of this setting
set OUTPUTFORMAT=minified


:: THESE SHOULD BE GOOD
set PATHTOTHEMES=C:\_d\websites\%SITENAME%\%ROOTDIRNAME%\wp-content\themes\
set PATHTODINCGALLERY=C:\_d\websites\%SITENAME%\%ROOTDIRNAME%\wp-content/mu-plugins/d_inc/d_gallery/css/partials

:: Set output path for compiled CSS files
set THEME_PATH=%PATHTOTHEMES%%CHILDNAME%

:: Set watch paths (semicolon-separated list of directories to watch)
set WATCH_PATHS=%PATHTOTHEMES%%CHILDNAME%\css
set WATCH_PATHS=%WATCH_PATHS%;%PATHTOTHEMES%d_theme_3\css
set WATCH_PATHS=%WATCH_PATHS%;%PATHTODINCGALLERY%

:: Add specific files to watch for import changes
set WATCH_FILES=%PATHTOTHEMES%%CHILDNAME%\css\style.scss

echo Running Gulp for %SITENAME% ...
:: Run gulp with the local gulpfile

node node_modules\gulp\bin\gulp.js --gulpfile gulpfile.js %*



:: Pause to see any error messages
if %ERRORLEVEL% neq 0 (
  echo Error occurred! Error level: %ERRORLEVEL%
  pause
)
