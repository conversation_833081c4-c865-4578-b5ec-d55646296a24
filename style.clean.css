body {
  font-family: "Arial", sans-serif;
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
  padding: 0;
  background-color: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header {
  background-color: #3498db;
  color: white;
  padding: 1rem 0;
}
.header h1 {
  margin: 0;
  font-size: 2rem;
}
.header nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
}
.header nav ul li {
  margin-right: 20px;
}
.header nav ul li a {
  color: white;
  text-decoration: none;
}
.header nav ul li a:hover {
  text-decoration: underline;
}

.btn {
  background-color: #3498db;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
.btn:hover {
  background-color: rgb(33.1380753138, 125.1882845188, 186.8619246862);
}
.btn.btn-secondary {
  background-color: #2ecc71;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
.btn.btn-secondary:hover {
  background-color: rgb(36.616, 162.384, 89.948);
}
.btn.btn-large {
  padding: 15px 30px;
  font-size: 1.2rem;
}

.content {
  padding: 2rem 0;
}
.content .card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1rem;
}
.content .card h2 {
  color: #3498db;
  margin-top: 0;
}
.content .card p {
  color: #666;
  line-height: 1.8;
}

.footer {
  background-color: #343a40;
  color: white;
  text-align: center;
  padding: 1rem 0;
  margin-top: 2rem;
}

@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }
  .header nav ul {
    flex-direction: column;
  }
  .header nav ul li {
    margin-right: 0;
    margin-bottom: 10px;
  }
  .btn {
    display: block;
    width: 100%;
    margin-bottom: 10px;
  }
}