 
// GULLLP - Central build system
const gulp = require('gulp');
const sass = require('gulp-sass')(require('sass'));
const cleanCSS = require('gulp-clean-css');
const rename = require('gulp-rename');
const sourcemaps = require('gulp-sourcemaps');
const watch = require('gulp-watch');
const path = require('path');
const fs = require('fs');
const minimist = require('minimist');

//add name add name add name add name add name add name add name add name 
const siteName = process.env.SITENAME || 'unknown';
const log = require('fancy-log');
gulp.on('start', e => {
  log(`[ ${siteName}] `);
});
//gulp.on('stop', e => {
 // log(`[ ${siteName}] `);
//});
//gulp.on('error', e => {
//  log(`[ ${siteName}] `);
//});
// end name end name end name end name end name end name end name end name end name



// Parse command line arguments
const args = minimist(process.argv.slice(2));

// Get compilation mode from command line arguments
const compilationMode = args.mode || 'default';
console.log(`Compilation mode: ${compilationMode}`);

// Get the theme path from environment variable
const themePath = process.env.THEME_PATH || '.';
console.log(`Output directory: ${themePath}`);

// Add this AFTER themePath is defined
const nodeSassRoot = process.cwd(); // Current working directory (should be _node-sass)
const cacheBaseDir = path.join(nodeSassRoot, '.cache');

// Convert the full theme path to a safe directory name
const projectName = themePath
  .replace(/[:\\\/]/g, '_') // Replace path separators with underscores
  .replace(/[^a-zA-Z0-9_-]/g, '_') // Replace other unsafe chars
  .replace(/_+/g, '_'); // Replace multiple consecutive underscores with a single one

const projectCacheDir = path.join(cacheBaseDir, projectName);

// Ensure cache directory exists
if (!fs.existsSync(cacheBaseDir)) {
  fs.mkdirSync(cacheBaseDir, { recursive: true });
}
if (!fs.existsSync(projectCacheDir)) {
  fs.mkdirSync(projectCacheDir, { recursive: true });
}

// Get watch paths from environment variable
const watchPathsEnv = process.env.WATCH_PATHS || '';
const watchDirs = watchPathsEnv.split(';').filter(dir => dir.trim() !== '');
console.log('Directories to watch:');
watchDirs.forEach(dir => console.log(` - ${dir}`));

// Get specific files to watch for import changes
const watchFilesEnv = process.env.WATCH_FILES || '';
const importWatchFiles = watchFilesEnv.split(';').filter(file => file.trim() !== '');
if (importWatchFiles.length > 0) {
	console.log('Specific files to watch for imports:');
	importWatchFiles.forEach(file => console.log(` - ${file}`));
}

// Get WordPress root directory (3 levels up from theme directory)
const wpRoot = path.resolve(themePath, '../../..');
console.log(`WordPress root: ${wpRoot}`);

// Find all SCSS files in watch directories
const scssFiles = [];
watchDirs.forEach(dir => {
	try {
		if (fs.existsSync(dir)) {
			const files = findScssFiles(dir);
			scssFiles.push(...files);
			console.log(`Found ${files.length} SCSS files in ${dir}`);
		} else {
			console.log(`Warning: Directory not found: ${dir}`);
		}
	} catch (err) {
		console.error(`Error scanning directory ${dir}:`, err);
	}
});

// Function to find all SCSS files in a directory
function findScssFiles(dir) {
	const results = [];
	const list = fs.readdirSync(dir);

	list.forEach(file => {
		const filePath = path.join(dir, file);
		const stat = fs.statSync(filePath);

		if (stat.isDirectory()) {
			// Skip node_modules and hidden directories
			if (!file.startsWith('.') && file !== 'node_modules') {
				results.push(...findScssFiles(filePath));
			}
		} else if (file.endsWith('.scss') || file.endsWith('.sass')) {
			results.push(filePath);
		}
	});

	return results;
}

// Function to check if imports have changed in a file
function checkImportsChanged(file) {
	try {
		if (!fs.existsSync(file)) {
			console.log(`Warning: File not found: ${file}`);
			return false;
		}

		const content = fs.readFileSync(file, 'utf8');
		const importRegex = /@import\s+['"]([^'"]+)['"]/g;
		const imports = [];
		let match;

		while ((match = importRegex.exec(content)) !== null) {
			imports.push(match[1]);
		}

		// Use the centralized cache directory
		const fileName = path.basename(file);
		const cacheFile = path.join(projectCacheDir, fileName + '.imports.json');
		let oldImports = [];

		if (fs.existsSync(cacheFile)) {
			oldImports = JSON.parse(fs.readFileSync(cacheFile, 'utf8'));
		}

		// Compare old and new imports
		const added = imports.filter(imp => !oldImports.includes(imp));
		const removed = oldImports.filter(imp => !imports.includes(imp));
		const changed = added.length > 0 || removed.length > 0;

		// Update cache if changed
		if (changed) {
			fs.writeFileSync(cacheFile, JSON.stringify(imports));
			console.log('Imports changed in ' + file);
			if (added.length > 0) console.log('Added: ' + added.join(', '));
			if (removed.length > 0) console.log('Removed: ' + removed.join(', '));
		}

		return changed;
	} catch (err) {
		console.error(`Error checking imports in ${file}:`, err);
		return false;
	}
}

// Get the main style file
const styleFile = path.join(themePath, 'css', 'style.scss');
console.log(`Main style file: ${styleFile}`);

// Define paths
const paths = {
	scss: scssFiles,
	style: styleFile,
	output: themePath
};

// Compile SCSS to style.debug.css with sourcemaps
function compileDebugSass() {
	return gulp.src(paths.style)
		.pipe(sourcemaps.init())
		.pipe(sass({
			outputStyle: 'expanded',
			sourceMap: true,
			quietDeps: true,
			quiet: true,
			silenceDeprecations: ['import'],
			includePaths: [
				...watchDirs,
				path.dirname(styleFile),
				wpRoot
			]
		}).on('error', sass.logError))
		.pipe(rename('style.debug.css'))
		.pipe(sourcemaps.write('.'))
		.pipe(gulp.dest(paths.output));
}

// Compile SCSS to style.css as minified CSS
function compileMinifiedSass() {
	return gulp.src(paths.style)
		.pipe(sass({
			outputStyle: 'compressed',
			quietDeps: true,
			quiet: true,
			silenceDeprecations: ['import'],
			includePaths: [
				...watchDirs,
				path.dirname(styleFile),
				wpRoot
			]
		}).on('error', sass.logError))
		.pipe(cleanCSS({compatibility: 'ie8'}))
		.pipe(rename('style.css'))
		.pipe(gulp.dest(paths.output));
}

// Compile SCSS to style.clean.css with expanded output but no sourcemaps
function compileCleanSass() {
	return gulp.src(paths.style)
		.pipe(sass({
			outputStyle: 'expanded',
			quietDeps: true,
			quiet: true,
			silenceDeprecations: ['import'],
			includePaths: [
				...watchDirs,
				path.dirname(styleFile),
				wpRoot
			]
		}).on('error', sass.logError))
		.pipe(rename('style.clean.css'))
		.pipe(gulp.dest(paths.output));
}

// Unified compilation function that handles different modes
function compileSass(mode) {
	switch(mode) {
		case 'debug':
			console.log('Compiling in debug mode...');
			return compileDebugSass();
		case 'minified':
			console.log('Compiling in minified mode...');
			return compileMinifiedSass();
		case 'clean':
			console.log('Compiling in clean mode...');
			return compileCleanSass();
		case 'all':
			console.log('Compiling in all modes...');
			return gulp.parallel(compileDebugSass, compileMinifiedSass, compileCleanSass)();
		default:
			console.log('Compiling in default mode (debug + minified)...');
			return gulp.parallel(compileDebugSass, compileMinifiedSass)();
	}
}

// Create task functions that return the compilation results properly
const compileAllModes = gulp.parallel(compileDebugSass, compileMinifiedSass, compileCleanSass);
const compileDefaultModes = gulp.parallel(compileDebugSass, compileMinifiedSass);

// Watch for changes in all SCSS files
function watchScssFiles() {
	console.log(`Watching ${paths.scss.length} SCSS files for changes...`);
	return watch(paths.scss, function() {
		return compileSass(compilationMode);
	});
}

// Watch for import changes in specific files
function watchImports() {
	if (importWatchFiles.length > 0) {
		console.log(`Watching ${importWatchFiles.length} files for import changes...`);
		return watch(importWatchFiles, function(cb) {
			let importChanged = false;

			importWatchFiles.forEach(file => {
				if (checkImportsChanged(file)) {
					importChanged = true;
				}
			});

			if (importChanged) {
				// Trigger a full rebuild using the specified compilation mode
				return compileSass(compilationMode);
			}
		});
	} else {
		return Promise.resolve();
	}
}

// Default task - uses compilation mode or defaults to debug + minified
exports.default = gulp.series(
	function() { return compileSass(compilationMode); },
	gulp.parallel(watchScssFiles, watchImports)
);

// Individual tasks
exports.debug = compileDebugSass;
exports.minified = compileMinifiedSass;
exports.clean = compileCleanSass;
exports.all = gulp.parallel(compileDebugSass, compileMinifiedSass, compileCleanSass);
exports.compile = function() { return compileSass(compilationMode); };
exports.watch = watchScssFiles;
exports.watchImports = watchImports;

// Legacy alias for backwards compatibility
exports.minify = compileMinifiedSass;
